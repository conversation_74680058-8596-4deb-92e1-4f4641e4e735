<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Services\UserService;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class UserPermissionLogicTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected UserService $userService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->userService = new UserService();
        
        // Create test permissions
        $this->createTestPermissions();
        
        // Create test roles with permissions
        $this->createTestRoles();
    }

    private function createTestPermissions(): void
    {
        $permissions = [
            ['name' => 'view_data', 'vi_name' => 'Xem dữ liệu', 'group' => 'data'],
            ['name' => 'create_data', 'vi_name' => 'Tạo dữ liệu', 'group' => 'data'],
            ['name' => 'edit_data', 'vi_name' => 'Sửa dữ liệu', 'group' => 'data'],
            ['name' => 'delete_data', 'vi_name' => 'Xóa dữ liệu', 'group' => 'data'],
            ['name' => 'view_users', 'vi_name' => 'Xem người dùng', 'group' => 'user'],
            ['name' => 'create_users', 'vi_name' => 'Tạo người dùng', 'group' => 'user'],
            ['name' => 'edit_users', 'vi_name' => 'Sửa người dùng', 'group' => 'user'],
            ['name' => 'delete_users', 'vi_name' => 'Xóa người dùng', 'group' => 'user'],
            ['name' => 'create_reports', 'vi_name' => 'Tạo báo cáo', 'group' => 'report'],
            ['name' => 'export_reports', 'vi_name' => 'Xuất báo cáo', 'group' => 'report'],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }
    }

    private function createTestRoles(): void
    {
        // Create admin role with data and user permissions
        $adminRole = Role::create([
            'name' => 'admin',
            'vi_name' => 'Quản trị viên',
            'description' => 'Quản lý người dùng và dữ liệu'
        ]);
        $adminRole->givePermissionTo(['view_data', 'create_data', 'edit_data', 'view_users', 'create_users', 'edit_users']);

        // Create viewer role with limited permissions
        $viewerRole = Role::create([
            'name' => 'viewer',
            'vi_name' => 'Người xem',
            'description' => 'Chỉ xem dữ liệu'
        ]);
        $viewerRole->givePermissionTo(['view_data']);
    }

    public function test_user_creation_with_role_and_additional_permissions(): void
    {
        // Test creating user with admin role and additional report permissions
        $userData = [
            'name' => 'Test Admin User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'roles' => ['admin'],
            'permissions' => ['create_reports', 'export_reports'] // Additional permissions beyond role
        ];

        $user = $this->userService->create($userData);

        // Verify user was created
        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('Test Admin User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);

        // Verify role assignment
        $this->assertTrue($user->hasRole('admin'));

        // Verify permissions (should include both role permissions and additional permissions)
        $expectedPermissions = [
            'view_data', 'create_data', 'edit_data', 'view_users', 'create_users', 'edit_users', // From admin role
            'create_reports', 'export_reports' // Additional permissions
        ];

        foreach ($expectedPermissions as $permission) {
            $this->assertTrue($user->hasPermissionTo($permission), "User should have permission: {$permission}");
        }

        // Verify user doesn't have permissions not assigned
        $this->assertFalse($user->hasPermissionTo('delete_data'));
        $this->assertFalse($user->hasPermissionTo('delete_users'));
    }

    public function test_user_update_with_role_change(): void
    {
        // Create a user with viewer role
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);
        $user->assignRole('viewer');
        $user->givePermissionTo('create_reports'); // Additional permission

        // Update user to admin role with different additional permissions
        $updateData = [
            'name' => 'Updated Test User',
            'roles' => ['admin'],
            'permissions' => ['export_reports'] // New additional permissions
        ];

        $updatedUser = $this->userService->update($user->id, $updateData);

        // Verify role change
        $this->assertTrue($updatedUser->hasRole('admin'));
        $this->assertFalse($updatedUser->hasRole('viewer'));

        // Verify permissions from new role
        $adminPermissions = ['view_data', 'create_data', 'edit_data', 'view_users', 'create_users', 'edit_users'];
        foreach ($adminPermissions as $permission) {
            $this->assertTrue($updatedUser->hasPermissionTo($permission), "User should have admin permission: {$permission}");
        }

        // Verify additional permission
        $this->assertTrue($updatedUser->hasPermissionTo('export_reports'));

        // Verify old additional permission is removed
        $this->assertFalse($updatedUser->hasPermissionTo('create_reports'));
    }

    public function test_user_update_preserves_role_permissions(): void
    {
        // Create a user with admin role
        $user = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);
        $user->assignRole('admin');

        // Update user with additional permissions only (no role change)
        $updateData = [
            'permissions' => ['create_reports', 'export_reports']
        ];

        $updatedUser = $this->userService->update($user->id, $updateData);

        // Verify role is preserved
        $this->assertTrue($updatedUser->hasRole('admin'));

        // Verify role permissions are preserved
        $adminPermissions = ['view_data', 'create_data', 'edit_data', 'view_users', 'create_users', 'edit_users'];
        foreach ($adminPermissions as $permission) {
            $this->assertTrue($updatedUser->hasPermissionTo($permission), "User should still have admin permission: {$permission}");
        }

        // Verify additional permissions are added
        $this->assertTrue($updatedUser->hasPermissionTo('create_reports'));
        $this->assertTrue($updatedUser->hasPermissionTo('export_reports'));
    }

    public function test_api_role_endpoint_returns_permissions(): void
    {
        // Test the API endpoint that returns role with permissions
        $response = $this->getJson('/api/roles/admin');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'name',
                        'vi_name',
                        'description',
                        'permissions' => [
                            '*' => [
                                'id',
                                'name',
                                'vi_name',
                                'group'
                            ]
                        ]
                    ]
                ]);

        $roleData = $response->json('data');
        $this->assertEquals('admin', $roleData['name']);
        $this->assertCount(6, $roleData['permissions']); // Admin role should have 6 permissions
    }

    public function test_api_permissions_endpoint_returns_all_permissions(): void
    {
        // Test the API endpoint that returns all permissions
        $response = $this->getJson('/api/permissions');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'vi_name',
                            'group'
                        ]
                    ]
                ]);

        $permissions = $response->json('data');
        $this->assertCount(10, $permissions); // Should have 10 test permissions
    }
}
